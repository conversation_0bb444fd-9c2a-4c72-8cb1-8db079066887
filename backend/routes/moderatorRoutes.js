const express = require('express');
const router = express.Router();
const moderatorController = require('../controllers/moderatorController');
const auth = require('../middleware/auth');

// Approve or reject a teacher's approval request
router.put('/teacher-approval', auth, moderatorController.manageTeacherApproval);

// Pause a user's account
router.post('/pause-account', auth, moderatorController.pauseAccount);

module.exports = router;