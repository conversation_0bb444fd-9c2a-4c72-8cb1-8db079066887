const express = require('express');
const router = express.Router();
const lessonController = require('../controllers/lessonController');
const auth = require('../middleware/auth');

// Book a lesson or Learning Plan
router.post('/book', auth, lessonController.bookLesson);

// Accept or reject a lesson booking request
router.put('/manage', auth, lessonController.manageBooking);

module.exports = router;