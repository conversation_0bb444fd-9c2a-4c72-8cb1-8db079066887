const express = require('express');
const router = express.Router();
const teacherController = require('../controllers/teacherController');
const auth = require('../middleware/auth');

// Update teacher profile
router.put('/profile', auth, teacherController.updateTeacherProfile);

// Submit teacher approval request
router.post('/approval', auth, teacherController.submitApprovalRequest);

// Add feedback or quiz URL to a lesson
router.put('/lesson/feedback', auth, teacherController.addLessonFeedback);

// Get dashboard data (profile, schedule, stats)
router.get('/dashboard', auth(['teacher']), teacherController.getDashboardData); // Example
router.get('/', auth, teacherController.forTest);

module.exports = router;