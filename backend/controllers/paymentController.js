const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const checkout = async (req, res) => {
  const { amount, currency, method } = req.body;
  try {
    const payment = await prisma.payment.create({
      data: {
        user_id: req.user.id,
        amount,
        currency,
        method,
        status: 'pending',
      },
    });
    // In a real implementation, integrate with ZainCash, AsiaPay, or Stripe here
    res.json({ message: 'Payment initiated', paymentId: payment.id });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

const getPaymentStatus = async (req, res) => {
  try {
    const payment = await prisma.payment.findUnique({
      where: { id: parseInt(req.params.id) },
    });
    res.json(payment);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { checkout, getPaymentStatus };