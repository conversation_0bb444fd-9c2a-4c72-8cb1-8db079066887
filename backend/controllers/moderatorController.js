const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Approves or rejects a teacher's approval request.
// - Input: JSON body with request_id, status ("approved" or "rejected"), comments (optional); JWT in the Authorization header.
// - Output: JSON response with a success message, or an error message if the update fails.
const manageTeacherApproval = async (req, res) => {
  const { request_id, status, comments } = req.body;
  try {
    if (req.user.role !== 'moderator') return res.status(403).json({ error: 'Unauthorized' });
    const request = await prisma.approvalRequest.update({
      where: { id: request_id },
      data: { status, comments, updated_at: new Date() },
    });
    await prisma.teacher.update({
      where: { id: request.teacher_id },
      data: { verified_status: status },
    });
    await prisma.notification.create({
      data: {
        recipient_id: request.teacher_id,
        type: 'teacher_approval',
        message: `Your approval request was ${status}${comments ? `: ${comments}` : ''}`,
        status: 'sent',
      },
    });
    await prisma.auditLog.create({
      data: {
        actor_id: req.user.id,
        action: `teacher_approval_${status}`,
        details: { request_id, comments },
      },
    });
    res.json({ message: `Teacher ${status}` });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Pauses a user's account by setting their status to 'paused'.
// - Input: JSON body with user_id; JWT in the Authorization header.
// - Output: JSON response with a success message, or an error message if the update fails.
const pauseAccount = async (req, res) => {
  const { user_id } = req.body;
  try {
    if (req.user.role !== 'moderator') return res.status(403).json({ error: 'Unauthorized' });
    await prisma.user.update({
      where: { id: user_id },
      data: { status: 'paused' },
    });
    await prisma.notification.create({
      data: {
        recipient_id: user_id,
        type: 'account_paused',
        message: 'Your account has been paused by a moderator',
        status: 'sent',
      },
    });
    await prisma.auditLog.create({
      data: {
        actor_id: req.user.id,
        action: 'pause_account',
        details: { user_id },
      },
    });
    res.json({ message: 'Account paused' });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { manageTeacherApproval, pauseAccount };