const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const getDashboardData = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('getDashboardData: userId:', userId);

    const parent = await prisma.parent.findUnique({
      where: { user_id: userId },
      include: {
        students: { include: { user: true, lessons: true } },
      },
    });
    console.log('getDashboardData: parent:', JSON.stringify(parent, null, 2));

    if (!parent) {
      console.log('getDashboardData: Parent not found for userId:', userId);
      return res.status(404).json({ error: 'Parent not found' });
    }

    const children = parent.students.length
      ? parent.students.map(student => ({
          name: student.user.name || 'Unknown',
          grade: student.grade_level || 'N/A',
          totalSessions: student.lessons.length,
          completedSessions: student.lessons.filter(l => l.status === 'completed').length,
          averageGrade: student.lessons.length
            ? Math.round(student.lessons.reduce((sum, l) => sum + (parseFloat(l.grade) || 0), 0) / student.lessons.length)
            : 0,
          subjects: [...new Set(student.lessons.map(l => l.subject).filter(Boolean))],
        }))
      : [];
    console.log('getDashboardData: children:', JSON.stringify(children, null, 2));

    const pendingApprovals = await prisma.approvalRequest.findMany({
      where: { teacher: { lessons: { some: { student: { parents: { some: { id: parent.id } } } } } }, status: 'pending' },
      include: { teacher: { include: { user: true } } },
    });
    const formattedApprovals = pendingApprovals.length
      ? pendingApprovals.map(req => ({
          child: 'N/A', // child_name not in schema; could fetch from related lesson
          teacher: req.teacher?.user.name || 'Unknown',
          subject: 'N/A', // subject not in schema; could fetch from related lesson
          date: req.created_at.toISOString(),
          duration: 1, // default since not in schema
          cost: 'N/A', // default since not in schema
          subscriptionId: req.id,
        }))
      : [];
    console.log('getDashboardData: pendingApprovals:', JSON.stringify(formattedApprovals, null, 2));

    const recentReports = await prisma.lesson.findMany({
      where: {
        student_id: { in: parent.students.map(s => s.id) },
        feedback: { not: null },
      },
      include: {
        teacher: { include: { user: true } },
        student: { include: { user: true } },
      },
      take: 5,
      orderBy: { scheduled_time: 'desc' },
    });
    const formattedReports = recentReports.length
      ? recentReports.map(lesson => ({
          child: lesson.student?.user.name || 'Unknown',
          subject: lesson.subject || 'N/A',
          teacher: lesson.teacher?.user.name || 'Unknown',
          date: lesson.scheduled_time.toISOString(),
          grade: lesson.grade || 'N/A',
          feedback: lesson.feedback || '',
        }))
      : [];
    console.log('getDashboardData: recentReports:', JSON.stringify(formattedReports, null, 2));

    const weeklySchedule = await prisma.lesson.findMany({
      where: {
        student_id: { in: parent.students.map(s => s.id) },
        scheduled_time: {
          gte: new Date(),
          lte: new Date(new Date().setDate(new Date().getDate() + 7)),
        },
      },
      include: { student: { include: { user: true } } },
    });
    const formattedSchedule = weeklySchedule.length
      ? weeklySchedule.map(lesson => ({
          child: lesson.student?.user.name || 'Unknown',
          subject: lesson.subject || 'N/A',
          time: lesson.scheduled_time.toISOString(),
          status: lesson.status,
        }))
      : [];
    console.log('getDashboardData: weeklySchedule:', JSON.stringify(formattedSchedule, null, 2));

    res.status(200).json({ children, pendingApprovals: formattedApprovals, recentReports: formattedReports, weeklySchedule: formattedSchedule });
  } catch (error) {
    console.error('getDashboardData error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to fetch dashboard data', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const getChildren = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('getChildren: userId:', userId);

    const parent = await prisma.parent.findUnique({
      where: { user_id: userId },
      include: { students: { include: { user: true } } },
    });
    console.log('getChildren: parent:', JSON.stringify(parent, null, 2));

    if (!parent) {
      console.log('getChildren: Parent not found for userId:', userId);
      return res.status(404).json({ error: 'Parent not found' });
    }

    const children = parent.students.length
      ? parent.students.map(student => ({
          id: student.id,
          name: student.user.name || 'Unknown',
        }))
      : [];
    console.log('getChildren: children:', JSON.stringify(children, null, 2));

    res.status(200).json(children);
  } catch (error) {
    console.error('getChildren error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to fetch children', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const getTeachers = async (req, res) => {
  try {
    console.log('getTeachers: Fetching teachers');
    const teachers = await prisma.teacher.findMany({
      include: { user: true },
    });
    console.log('getTeachers: teachers:', JSON.stringify(teachers, null, 2));

    const teacherData = teachers.length
      ? teachers.map(teacher => ({
          id: teacher.id,
          name: teacher.user.name || 'Unknown',
          subjects: teacher.subjects || [],
        }))
      : [];
    console.log('getTeachers: teacherData:', JSON.stringify(teacherData, null, 2));

    res.status(200).json(teacherData);
  } catch (error) {
    console.error('getTeachers error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to fetch teachers', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const scheduleSession = async (req, res) => {
  try {
    const { childId, teacherId, subject, date, time } = req.body;
    const userId = req.user.id;
    console.log('scheduleSession: Request body:', JSON.stringify(req.body, null, 2), 'userId:', userId);

    if (!childId || !teacherId || !subject || !date || !time) {
      console.log('scheduleSession: Missing required fields');
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const parent = await prisma.parent.findUnique({ where: { user_id: userId } });
    console.log('scheduleSession: parent:', JSON.stringify(parent, null, 2));
    if (!parent) {
      console.log('scheduleSession: Parent not found for userId:', userId);
      return res.status(404).json({ error: 'Parent not found' });
    }

    const student = await prisma.student.findUnique({ where: { id: parseInt(childId) } });
    console.log('scheduleSession: student:', JSON.stringify(student, null, 2));
    if (!student || !student.parents.some(p => p.id === parent.id)) {
      console.log('scheduleSession: Invalid or unlinked childId:', childId);
      return res.status(400).json({ error: 'Invalid or unlinked child' });
    }

    const teacher = await prisma.teacher.findUnique({ where: { id: parseInt(teacherId) } });
    console.log('scheduleSession: teacher:', JSON.stringify(teacher, null, 2));
    if (!teacher) {
      console.log('scheduleSession: Teacher not found for teacherId:', teacherId);
      return res.status(404).json({ error: 'Teacher not found' });
    }

    const scheduledTime = new Date(`${date}T${time}:00.000Z`);
    if (isNaN(scheduledTime.getTime())) {
      console.log('scheduleSession: Invalid date or time:', date, time);
      return res.status(400).json({ error: 'Invalid date or time' });
    }

    const lesson = await prisma.lesson.create({
      data: {
        teacher_id: parseInt(teacherId),
        student_id: parseInt(childId),
        subject,
        scheduled_time: scheduledTime,
        status: 'pending',
      },
    });
    console.log('scheduleSession: Created lesson:', JSON.stringify(lesson, null, 2));

    const notification = await prisma.notification.create({
      data: {
        recipient_id: teacher.user_id,
        type: 'lesson_request',
        message: `New lesson request for ${subject} on ${date} at ${time}`,
        status: 'sent',
      },
    });
    console.log('scheduleSession: Created notification:', JSON.stringify(notification, null, 2));

    res.status(200).json({ message: 'Session scheduled successfully', lesson });
  } catch (error) {
    console.error('scheduleSession error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to schedule session', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const linkChild = async (req, res) => {
  try {
    const { student_id } = req.body;
    const userId = req.user.id;
    console.log('linkChild: Request body:', JSON.stringify(req.body, null, 2), 'userId:', userId);

    if (!student_id) {
      console.log('linkChild: Missing student_id');
      return res.status(400).json({ error: 'Missing student_id' });
    }

    const parent = await prisma.parent.findUnique({ where: { user_id: userId } });
    console.log('linkChild: parent:', JSON.stringify(parent, null, 2));
    if (!parent) {
      console.log('linkChild: Parent not found for userId:', userId);
      return res.status(404).json({ error: 'Parent not found' });
    }

    const student = await prisma.student.findUnique({
      where: { id: parseInt(student_id) },
      include: { parents: true },
    });
    console.log('linkChild: student:', JSON.stringify(student, null, 2));
    if (!student) {
      console.log('linkChild: Student not found for student_id:', student_id);
      return res.status(404).json({ error: 'Student not found' });
    }

    if (student.parents.some(p => p.id === parent.id)) {
      console.log('linkChild: Student already linked to this parent, student_id:', student_id);
      return res.status(400).json({ error: 'Student already linked to this parent' });
    }

    const updatedStudent = await prisma.student.update({
      where: { id: parseInt(student_id) },
      data: { parents: { connect: { id: parent.id } } },
    });
    console.log('linkChild: Updated student:', JSON.stringify(updatedStudent, null, 2));

    res.status(200).json({ message: 'Child linked successfully' });
  } catch (error) {
    console.error('linkChild error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to link child', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const getReports = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('getReports: userId:', userId);

    const parent = await prisma.parent.findUnique({
      where: { user_id: userId },
      include: {
        students: {
          include: {
            lessons: {
              include: {
                teacher: { include: { user: true } },
                student: { include: { user: true } },
              },
            },
          },
        },
      },
    });
    console.log('getReports: parent:', JSON.stringify(parent, null, 2));

    if (!parent) {
      console.log('getReports: Parent not found for userId:', userId);
      return res.status(404).json({ error: 'Parent not found' });
    }

    const reports = parent.students.length
      ? parent.students.flatMap(student =>
          student.lessons
            .filter(lesson => lesson.feedback)
            .map(lesson => ({
              child: student.user.name || 'Unknown',
              subject: lesson.subject || 'N/A',
              teacher: lesson.teacher?.user.name || 'Unknown',
              date: lesson.scheduled_time.toISOString(),
              grade: lesson.grade || 'N/A',
              feedback: lesson.feedback || '',
            }))
        )
      : [];
    console.log('getReports: reports:', JSON.stringify(reports, null, 2));

    res.status(200).json(reports);
  } catch (error) {
    console.error('getReports error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to fetch reports', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

const signPlan = async (req, res) => {
  try {
    const { subscription_id, signature } = req.body;
    const userId = req.user.id;
    console.log('signPlan: Request body:', JSON.stringify(req.body, null, 2), 'userId:', userId);

    if (!subscription_id || !signature) {
      console.log('signPlan: Missing subscription_id or signature');
      return res.status(400).json({ error: 'Missing subscription_id or signature' });
    }

    const parent = await prisma.parent.findUnique({ where: { user_id: userId } });
    console.log('signPlan: parent:', JSON.stringify(parent, null, 2));
    if (!parent) {
      console.log('signPlan: Parent not found for userId:', userId);
      return res.status(404).json({ error: 'Parent not found' });
    }

    const subscription = await prisma.subscription.findUnique({
      where: { id: parseInt(subscription_id) },
      include: { student: { include: { parents: true } } },
    });
    console.log('signPlan: subscription:', JSON.stringify(subscription, null, 2));
    if (!subscription) {
      console.log('signPlan: Subscription not found for subscription_id:', subscription_id);
      return res.status(404).json({ error: 'Subscription not found' });
    }

    if (!subscription.student.parents.some(p => p.id === parent.id)) {
      console.log('signPlan: Parent not linked to subscription’s student, subscription_id:', subscription_id);
      return res.status(403).json({ error: 'Parent not authorized for this subscription' });
    }

    const eSignature = await prisma.eSignature.create({
      data: {
        parent_id: parent.id,
        subscription_id: parseInt(subscription_id),
        signature,
      },
    });
    console.log('signPlan: Created eSignature:', JSON.stringify(eSignature, null, 2));

    const updatedSubscription = await prisma.subscription.update({
      where: { id: parseInt(subscription_id) },
      data: { status: 'approved' },
    });
    console.log('signPlan: Updated subscription:', JSON.stringify(updatedSubscription, null, 2));

    res.status(200).json({ message: 'Plan signed successfully', eSignature });
  } catch (error) {
    console.error('signPlan error:', error.name, error.message, error.stack);
    res.status(500).json({ error: 'Failed to sign plan', details: error.message });
  } finally {
    await prisma.$disconnect();
  }
};

module.exports = {
  getDashboardData,
  getChildren,
  getTeachers,
  scheduleSession,
  linkChild,
  getReports,
  signPlan,
};