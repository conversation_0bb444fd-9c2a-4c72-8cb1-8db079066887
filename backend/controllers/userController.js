const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");

const prisma = new PrismaClient();

// Registers a new user in the EducationPlatform database, creating a User record and, if applicable, a linked Student, Teacher, or Parent record.
// - Input: JSON body with name, email, phone, password, role, language, and (for students) grade_level.
// - Output: JSON response with a success message and the created user's ID, or an error message if registration fails.
const register = async (req, res) => {
  const { name, email, phone, password, role, language, grade_level } = req.body;
  try {
    const hashedPassword = await bcrypt.hash(password, 10);
    const user = await prisma.user.create({
      data: {
        name,
        email,
        phone,
        password_hash: hashedPassword,
        role,
        language,
        status: role === "teacher" ? "pending" : "active",
        ...(role === "student" && { student: { create: { grade_level } } }),
        ...(role === "teacher" && { teacher: { create: { subjects: [], rates: 0, availability: {}, verified_status: "pending", certificates: [] } } }),
        ...(role === "parent" && { parent: { create: {} } }),
      },
    });
    // Generate OTP for email confirmation
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    await prisma.otp.create({
      data: {
        user_id: user.id,
        code: otpCode,
        expires_at: new Date(Date.now() + 10 * 60 * 1000), // Expires in 10 minutes
      },
    });
    // In a real app, send OTP via email (not implemented here)
    res.status(201).json({ message: "User registered, OTP sent", userId: user.id, otpCode });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Confirms a user's registration by verifying their OTP code.
// - Input: JSON body with user_id and code.
// - Output: JSON response with a success message, or an error message if OTP is invalid or expired.
const confirmOtp = async (req, res) => {
  const { user_id, code } = req.body;
  try {
    const otp = await prisma.otp.findFirst({
      where: { user_id, code, expires_at: { gt: new Date() } },
    });
    if (!otp) return res.status(400).json({ error: "Invalid or expired OTP" });
    await prisma.user.update({
      where: { id: user_id },
      data: { status: "active" },
    });
    await prisma.otp.delete({ where: { id: otp.id } });
    res.json({ message: "OTP confirmed, user activated" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Authenticates a user by verifying their email and password, returning a JWT for session management.
// - Input: JSON body with email and password.
// - Output: JSON response with a JWT containing user ID, role, and email, or an error message if authentication fails.
const login = async (req, res) => {
  const { email, password } = req.body;
  console.log("login: Attempting login for email:", email);
  try {
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      console.log("login: User not found for email:", email);
      return res.status(401).json({ error: "Invalid email or password" });
    }
    const isValid = await bcrypt.compare(password, user.password_hash);
    if (!isValid) {
      console.log("login: Invalid password for email:", email);
      return res.status(401).json({ error: "Invalid email or password" });
    }
    const token = jwt.sign({ id: user.id, role: user.role }, process.env.JWT_SECRET, { expiresIn: "1h" });
    console.log("login: Token generated for user:", { id: user.id, role: user.role });
    res.status(200).json({ token });
  } catch (error) {
    console.error("login: Error:", error);
    res.status(500).json({ error: "Login failed" });
  } finally {
    await prisma.$disconnect();
  }
};

// Retrieves the profile of the authenticated user, including associated Student, Teacher, or Parent data.
// - Input: JWT in the Authorization header (verified by authMiddleware).
// - Output: JSON response with the user's details, including linked student, teacher, or parent records, or an error message if the user is not found.
const getProfile = async (req, res) => {
  try {
    console.log("getProfile: Fetching profile for user ID:", req.user.id);
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: { student: true, teacher: true, parent: true },
    });

    if (!user) {
      console.log("getProfile: User not found for ID:", req.user.id);
      return res.status(404).json({ error: "User not found" });
    }

    console.log("getProfile: User found, sending response");
    res.json(user);
  } catch (error) {
    console.error("getProfile: Error:", error);
    res.status(400).json({ error: error.message });
  }
};

// Updates the authenticated user's profile information in the User table.
// - Input: JSON body with name, email, phone, and language; JWT in the Authorization header.
// - Output: JSON response with the updated user data, or an error message if the update fails.
const updateProfile = async (req, res) => {
  const { name, email, phone, language } = req.body;
  try {
    const user = await prisma.user.update({
      where: { id: req.user.id },
      data: { name, email, phone, language },
    });
    res.json(user);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Soft-deletes a user by setting their status to 'deleted' in the User table.
// - Input: User ID in the URL parameter; JWT in the Authorization header (must be authorized to delete).
// - Output: JSON response with a success message, or an error message if the deletion fails.
const deleteUser = async (req, res) => {
  try {
    await prisma.user.update({
      where: { id: parseInt(req.params.id) },
      data: { status: "deleted" },
    });
    res.json({ message: "User deleted" });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Requests a GDPR data export or deletion for the authenticated user.
// - Input: JSON body with type ("export" or "delete"); JWT in the Authorization header.
// - Output: JSON response with a success message and request ID, or an error message if the request fails.
const requestData = async (req, res) => {
  const { type } = req.body;
  try {
    if (!["export", "delete"].includes(type)) {
      return res.status(400).json({ error: "Invalid request type" });
    }
    const dataRequest = await prisma.dataRequest.create({
      data: {
        user_id: req.user.id,
        type,
        status: "pending",
      },
    });
    res.status(201).json({ message: "Data request created", requestId: dataRequest.id });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { register, confirmOtp, login, getProfile, updateProfile, deleteUser, requestData };
