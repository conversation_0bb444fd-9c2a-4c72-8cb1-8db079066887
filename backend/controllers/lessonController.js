const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Books a lesson or Learning Plan for a student with a teacher.
// - Input: JSON body with teacher_id, student_id, scheduled_time, plan_type ("single_lesson" or "learning_plan"); JWT in the Authorization header.
// - Output: JSON response with a success message and lesson/subscription ID, or an error message if booking fails.
const bookLesson = async (req, res) => {
  const { teacher_id, student_id, scheduled_time, plan_type } = req.body;
  try {
    if (req.user.role !== 'student' && req.user.role !== 'parent') return res.status(403).json({ error: 'Unauthorized' });
    if (plan_type === 'single_lesson') {
      const lesson = await prisma.lesson.create({
        data: {
          teacher_id,
          student_id,
          scheduled_time: new Date(scheduled_time),
          status: 'scheduled',
        },
      });
      await prisma.notification.create({
        data: {
          recipient_id: teacher_id,
          type: 'booking_request',
          message: `New lesson booked by student ${student_id}`,
          status: 'sent',
        },
      });
      res.status(201).json({ message: 'Lesson booked', lessonId: lesson.id });
    } else if (plan_type === 'learning_plan') {
      const subscription = await prisma.subscription.create({
        data: {
          student_id,
          plan_type,
          num_sessions: 10, // Example default
          price: 100.0, // Example default
          status: 'pending',
        },
      });
      await prisma.notification.create({
        data: {
          recipient_id: teacher_id,
          type: 'booking_request',
          message: `New Learning Plan booked by student ${student_id}`,
          status: 'sent',
        },
      });
      res.status(201).json({ message: 'Learning Plan booked', subscriptionId: subscription.id });
    } else {
      res.status(400).json({ error: 'Invalid plan_type' });
    }
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

// Accepts or rejects a lesson booking request.
// - Input: JSON body with lesson_id, status ("accepted" or "rejected"); JWT in the Authorization header.
// - Output: JSON response with a success message, or an error message if the update fails.
const manageBooking = async (req, res) => {
  const { lesson_id, status } = req.body;
  try {
    if (req.user.role !== 'teacher') return res.status(403).json({ error: 'Unauthorized' });
    const lesson = await prisma.lesson.update({
      where: { id: lesson_id, teacher_id: req.user.id },
      data: { status: status === 'accepted' ? 'scheduled' : 'canceled' },
    });
    await prisma.notification.create({
      data: {
        recipient_id: lesson.student_id,
        type: 'booking_response',
        message: `Your lesson booking was ${status}`,
        status: 'sent',
      },
    });
    res.json({ message: `Booking ${status}` });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { bookLesson, manageBooking };