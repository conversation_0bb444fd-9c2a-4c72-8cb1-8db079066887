const { PrismaClient } = require('@prisma/client');
const path = require('path');
const fs = require('fs').promises;

const prisma = new PrismaClient();

const getDashboardData = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('getStudentDashboardData: Extracted userId:', userId);

    const student = await prisma.student.findUnique({
      where: { user_id: userId },
      include: {
        user: true,
        lessons: {
          include: {
            teacher: { include: { user: true } },
            assignments: true,
          },
        },
        subscription: true,
      },
    });

    if (!student) {
      console.log('getStudentDashboardData: Student not found for userId:', userId);
      return res.status(404).json({ error: 'Student not found' });
    }

    // Fetch teachers associated with the student's lessons
    const teachers = await prisma.teacher.findMany({
      where: {
        lessons: {
          some: { student_id: student.id },
        },
      },
      include: { user: true },
    });

    // Fetch lessons for the schedule
    const lessons = student.lessons.map(lesson => ({
      id: lesson.id,
      title: lesson.subject || 'Untitled Lesson',
      date: lesson.scheduled_time.toISOString(),
      duration: 60, // Assume 60 minutes; adjust if duration is stored
      teacher: lesson.teacher.user.name,
    }));

    // Derive courses from lessons (group by subject)
    const courses = [...new Set(student.lessons.map(l => l.subject))]
      .filter(subject => subject)
      .map((subject, index) => ({
        id: index + 1,
        title: subject,
        instructor: student.lessons.find(l => l.subject === subject)?.teacher.user.name || 'Unknown',
        progress: Math.floor(Math.random() * 50) + 50, // Mock progress; replace with real data
        nextClass: student.lessons.find(l => l.subject === subject)?.scheduled_time.toISOString() || new Date().toISOString(),
        color: ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-pink-500'][index % 4],
      }));

    // Fetch assignments
    const assignments = await prisma.assignment.findMany({
      where: { student_id: student.id },
      include: { lesson: true },
    });

    // Calculate stats
    const totalCourses = courses.length;
    const assignmentsDue = assignments.filter(a => a.status === 'pending' && new Date(a.due_date) <= new Date(new Date().setDate(new Date().getDate() + 7))).length;
    const gpa = 3.8; // Mock GPA; replace with real data
    const studyHours = student.lessons.reduce((sum, lesson) => sum + 1, 0); // Assume 1 hour per lesson

    const response = {
      profile: {
        name: student.user.name,
        email: student.user.email,
        grade_level: student.grade_level,
      },
      courses,
      assignments: assignments.map(a => ({
        id: a.id,
        title: a.title,
        course: a.lesson?.subject || a.subject,
        dueDate: a.due_date.toISOString(),
        priority: a.priority,
        status: a.status,
      })),
      teachers: teachers.map(t => ({
        id: t.id,
        name: t.user.name,
        subject: t.subjects.join(', '),
        email: t.user.email,
      })),
      schedule: lessons,
      stats: {
        totalCourses,
        assignmentsDue,
        gpa,
        studyHours,
      },
    };

    console.log('getStudentDashboardData: Response:', response);
    res.json(response);
  } catch (error) {
    console.error('getStudentDashboardData: Error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const uploadAssignment = async (req, res) => {
  try {
    const { assignmentId } = req.body;
    const file = req.file;

    if (!assignmentId || !file) {
      return res.status(400).json({ error: 'Assignment ID and file are required' });
    }

    const assignment = await prisma.assignment.findUnique({
      where: { id: parseInt(assignmentId) },
    });

    if (!assignment) {
      return res.status(404).json({ error: 'Assignment not found' });
    }

    const filePath = `/uploads/assignments/${file.filename}`;
    await prisma.assignment.update({
      where: { id: parseInt(assignmentId) },
      data: {
        status: 'submitted',
        submission_url: filePath,
        updated_at: new Date(),
      },
    });

    res.json({ message: 'Assignment submitted successfully', submission_url: filePath });
  } catch (error) {
    console.error('uploadAssignment: Error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = { getDashboardData, uploadAssignment };