const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const createReport = async (req, res) => {
  const { type, description } = req.body;
  try {
    const report = await prisma.report.create({
      data: {
        user_id: req.user.id,
        type,
        description,
        status: 'open',
      },
    });
    res.status(201).json({ message: 'Report created', reportId: report.id });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { createReport };