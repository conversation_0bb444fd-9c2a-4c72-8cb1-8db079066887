const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const jwt = require('jsonwebtoken');
const path = require('path');

// Configure multer for file uploads
const multer = require('multer');
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, 'teacher-' + uniqueSuffix + path.extname(file.originalname));
  },
});
const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const filetypes = /jpeg|jpg|png|gif/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);
    if (extname && mimetype) {
      cb(null, true);
    } else {
      cb(new Error('Only images (jpeg, jpg, png, gif) are allowed'), false);
    }
  },
});

const updateTeacherProfile = async (req, res) => {
  try {
    const { subjects, rates, availability, intro_video_url, certificates } = req.body;
    const userId = req.user.id;
    const parsedSubjects = subjects ? JSON.parse(subjects) : undefined;

    const updateData = {
      subjects: parsedSubjects,
      rates: rates ? parseFloat(rates) : undefined,
      availability: availability ? JSON.parse(availability) : undefined,
      intro_video_url,
      certificates: certificates ? JSON.parse(certificates) : undefined,
    };

    if (req.file) {
      updateData.photo_path = `/uploads/${req.file.filename}`;
    }

    const teacher = await prisma.teacher.update({
      where: { user_id: userId },
      data: updateData,
      include: { user: true },
    });

    res.status(200).json({ message: 'Profile updated successfully', teacher });
  } catch (error) {
    console.error('updateTeacherProfile: Error:', error);
    res.status(500).json({ error: 'Failed to update profile', details: error.message });
  }
};

const submitApprovalRequest = async (req, res) => {
  try {
    const userId = req.user.id;
    const teacher = await prisma.teacher.findUnique({ where: { user_id: userId } });

    if (!teacher) {
      return res.status(404).json({ error: 'Teacher not found' });
    }

    const approvalRequest = await prisma.approvalRequest.create({
      data: {
        teacher_id: teacher.id,
        status: 'pending',
      },
    });

    await prisma.notification.create({
      data: {
        recipient_id: 1, // Admin or moderator ID
        teacher_id: teacher.id,
        type: 'approval_request',
        message: `New approval request from teacher ${teacher.user_id}`,
        status: 'sent',
      },
    });

    res.status(200).json({ message: 'Approval request submitted', approvalRequest });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to submit approval request' });
  }
};

const addLessonFeedback = async (req, res) => {
  try {
    const { lesson_id, feedback, quiz_url } = req.body;
    const userId = req.user.id;
    const teacher = await prisma.teacher.findUnique({ where: { user_id: userId } });

    if (!teacher) {
      return res.status(404).json({ error: 'Teacher not found' });
    }

    const lesson = await prisma.lesson.update({
      where: { id: lesson_id, teacher_id: teacher.id },
      data: { feedback, quiz_url, status: 'completed' },
    });

    await prisma.notification.create({
      data: {
        recipient_id: lesson.student_id,
        teacher_id: teacher.id,
        type: 'lesson_feedback',
        message: `Feedback added for lesson at ${lesson.scheduled_time}`,
        status: 'sent',
      },
    });

    res.status(200).json({ message: 'Feedback added successfully', lesson });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to add feedback' });
  }
};


const getDashboardData = async (req, res) => {
  try {
    const userId = req.user.id; // From JWT middleware
    console.log('getDashboardData: Extracted userId:', userId);

    const teacher = await prisma.teacher.findUnique({
      where: { user_id: userId },
      include: {
        user: true,
        stats: true,
      },
    });

    if (!teacher) {
      return res.status(404).json({ error: 'Teacher not found' });
    }

    // Define the start and end of the day in UTC
    const today = new Date('2025-07-04T00:00:00.000Z'); // Start of July 4, 2025 UTC
    const tomorrow = new Date('2025-07-05T00:00:00.000Z'); // Start of July 5, 2025 UTC
    console.log('getDashboardData: Today:', today, 'Tomorrow:', tomorrow);

    const lessons = await prisma.lesson.findMany({
      where: {
        teacher_id: teacher.id,
        scheduled_time: {
          gte: today,
          lt: tomorrow,
        },
      },
      include: {
        student: {
          include: { user: true },
        },
      },
    });
    console.log('getDashboardData: Lessons query result:', lessons);

    const response = {
      profile: {
        name: teacher.user.name,
        email: teacher.user.email,
        subjects: teacher.subjects,
        rating: teacher.rating,
        experience: 0, // Adjust if you have an experience field
        photo_path: teacher.photo_path ? `http://localhost:3000${teacher.photo_path}` : null,
      },
      schedule: lessons.map(lesson => ({
        time: lesson.scheduled_time.toISOString(),
        student: lesson.student.user.name,
        subject: lesson.subject,
        status: lesson.status,
      })),
      stats: teacher.stats,
    };

    console.log('getDashboardData: Response:', response);
    res.json(response);
  } catch (error) {
    console.error('getDashboardData: Error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const forTest = async (req, res) => {
  res.status(200).json('mahdi ben ahmed');
};

module.exports = {
  updateTeacherProfile: [upload.single('photo'), updateTeacherProfile], // Add multer middleware
  submitApprovalRequest,
  addLessonFeedback,
  getDashboardData,
  forTest,
};