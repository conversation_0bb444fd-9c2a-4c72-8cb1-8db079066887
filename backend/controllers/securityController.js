const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();
// Flags a user by creating a new record in the Flag table to report an issue or incident.
// - Input: JSON body with user_id (integer), type (string), and description (string).
// - Output: JSON response with a success message and the created flag's ID, or an error message if creation fails.
const flagUser = async (req, res) => {
  const { user_id, type, description } = req.body;
  try {
    const flag = await prisma.flag.create({
      data: {
        user_id,
        type,
        description,
        status: 'open',
      },
    });
    res.status(201).json({ message: 'User flagged', flagId: flag.id });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};
// Retrieves a list of flagged incidents from the Flag table, optionally filtered by type and status.
// - Input: Query parameters type (string, optional) and status (string, optional).
// - Output: JSON response with an array of flag records matching the filters, or an error message if the query fails.
const getIncidents = async (req, res) => {
  const { type, status } = req.query;
  try {
    const incidents = await prisma.flag.findMany({
      where: { type, status },
    });
    res.json(incidents);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

module.exports = { flagUser, getIncidents };