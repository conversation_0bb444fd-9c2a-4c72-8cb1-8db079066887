const jwt = require('jsonwebtoken');
require('dotenv').config();

const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NCwicm9sZSI6InN0dWRlbnQiLCJpYXQiOjE3NTE3NTg1NjMsImV4cCI6MTc1MTc2MjE2M30.Ce8hWSaGmyTt4ZQgcxBhetyA3gXOMY3LET5PSQuA8EY";

console.log('JWT_SECRET:', process.env.JWT_SECRET);
console.log('Token:', token);

try {
  const decoded = jwt.verify(token, process.env.JWT_SECRET);
  console.log('Decoded successfully:', decoded);
} catch (error) {
  console.error('JWT verification failed:', error.name, error.message);
}
