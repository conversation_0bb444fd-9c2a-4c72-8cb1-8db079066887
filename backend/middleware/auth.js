const jwt = require("jsonwebtoken");

const auth = (roles = []) => {
  return async (req, res, next) => {
    console.log("auth: Starting auth middleware");
    const authHeader = req.header("Authorization");
    console.log("auth: Authorization header:", authHeader);

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      console.log("auth: No token provided or invalid format");
      return res.status(401).json({ error: "No token provided or invalid format" });
    }

    const token = authHeader.replace("Bearer ", "");
    console.log("auth: Token extracted:", token);

    try {
      console.log("auth: About to verify JWT");
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log("auth: Decoded JWT payload:", decoded);

      if (roles.length && !roles.includes(decoded.role)) {
        console.log("auth: Forbidden - role not allowed:", decoded.role, "Expected:", roles);
        return res.status(403).json({ error: `Forbidden: Role ${decoded.role} not in allowed roles`, allowedRoles: roles });
      }

      req.user = decoded;
      console.log("auth: User authorized, calling next()");
      next();
      console.log("auth: next() called successfully");
    } catch (error) {
      console.error("auth: JWT verification error:", error.name, error.message);
      res.status(401).json({ error: "Invalid token", details: error.message });
    }
  };
};

module.exports = auth;
