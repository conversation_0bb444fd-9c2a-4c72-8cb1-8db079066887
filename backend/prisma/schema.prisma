generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Teacher {
  id                Int               @id @default(autoincrement())
  user_id           Int               @unique
  subjects          String[]
  rates             Float
  availability      Json
  verified_status   String
  intro_video_url   String?
  certificates      String[]
  organization_id   Int?
  approval_requests ApprovalRequest[]
  lessons           Lesson[]
  organization      Organization?     @relation(fields: [organization_id], references: [id], onDelete: Cascade)
  user              User              @relation(fields: [user_id], references: [id])
}

model Student {
  id              Int           @id @default(autoincrement())
  user_id         Int           @unique
  grade_level     String
  subscription_id Int?          @unique
  created_at      DateTime      @default(now())
  lessons         Lesson[]
  user            User          @relation(fields: [user_id], references: [id])
  subscription    Subscription?
  Parent          Parent[]      @relation("ParentStudent")
}

model Parent {
  id           Int          @id @default(autoincrement())
  user_id      Int          @unique
  e_signatures ESignature[]
  user         User         @relation(fields: [user_id], references: [id])
  Student      Student[]    @relation("ParentStudent")
}

model Lesson {
  id             Int      @id @default(autoincrement())
  teacher_id     Int
  student_id     Int
  scheduled_time DateTime
  status         String
  recording_url  String?
  feedback       String?
  quiz_url       String?
  created_at     DateTime @default(now())
  student        Student  @relation(fields: [student_id], references: [id])
  teacher        Teacher  @relation(fields: [teacher_id], references: [id])
}

model Organization {
  id            Int       @id @default(autoincrement())
  name          String
  admin_user_id Int       @unique
  created_at    DateTime  @default(now())
  admin         User      @relation("OrganizationAdmin", fields: [admin_user_id], references: [id])
  teachers      Teacher[]
}

model User {
  id            Int            @id @default(autoincrement())
  name          String
  email         String         @unique
  phone         String?        @unique
  password_hash String
  role          String
  language      String?
  status        String
  created_at    DateTime       @default(now())
  updated_at    DateTime       @updatedAt
  audit_logs    AuditLog[]
  data_requests DataRequest[]
  flags         Flag[]
  notifications Notification[]
  organization  Organization?  @relation("OrganizationAdmin")
  otps          Otp[]
  parent        Parent?
  payments      Payment[]
  reports       Report[]
  student       Student?
  teacher       Teacher?
}

model Subscription {
  id           Int          @id @default(autoincrement())
  student_id   Int          @unique
  plan_type    String
  num_sessions Int
  price        Float
  status       String
  created_at   DateTime     @default(now())
  updated_at   DateTime     @updatedAt
  e_signatures ESignature[]
  student      Student      @relation(fields: [student_id], references: [id], onDelete: Cascade)
}

model Payment {
  id          Int      @id @default(autoincrement())
  user_id     Int
  amount      Float
  currency    String
  method      String
  status      String
  receipt_url String?
  created_at  DateTime @default(now())
  user        User     @relation(fields: [user_id], references: [id])
}

model Report {
  id          Int      @id @default(autoincrement())
  user_id     Int
  type        String
  description String
  status      String
  created_at  DateTime @default(now())
  user        User     @relation(fields: [user_id], references: [id])
}

model Flag {
  id          Int      @id @default(autoincrement())
  user_id     Int
  type        String
  description String
  status      String
  created_at  DateTime @default(now())
  user        User     @relation(fields: [user_id], references: [id])
}

model Otp {
  id         Int      @id @default(autoincrement())
  user_id    Int
  code       String
  expires_at DateTime
  created_at DateTime @default(now())
  user       User     @relation(fields: [user_id], references: [id])
}

model ApprovalRequest {
  id         Int      @id @default(autoincrement())
  teacher_id Int
  status     String
  comments   String?
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  teacher    Teacher  @relation(fields: [teacher_id], references: [id])
}

model AuditLog {
  id         Int      @id @default(autoincrement())
  actor_id   Int
  action     String
  details    Json
  created_at DateTime @default(now())
  actor      User     @relation(fields: [actor_id], references: [id])
}

model DataRequest {
  id         Int      @id @default(autoincrement())
  user_id    Int
  type       String
  status     String
  created_at DateTime @default(now())
  user       User     @relation(fields: [user_id], references: [id])
}

model Notification {
  id           Int      @id @default(autoincrement())
  recipient_id Int
  type         String
  message      String
  status       String
  created_at   DateTime @default(now())
  recipient    User     @relation(fields: [recipient_id], references: [id])
}

model ESignature {
  id              Int          @id @default(autoincrement())
  parent_id       Int
  subscription_id Int
  signature       String
  created_at      DateTime     @default(now())
  parent          Parent       @relation(fields: [parent_id], references: [id])
  subscription    Subscription @relation(fields: [subscription_id], references: [id])
}
