/*
  Warnings:

  - You are about to drop the column `subscription_id` on the `Student` table. All the data in the column will be lost.
  - You are about to drop the `_ParentStudent` table. If the table is not empty, all the data it contains will be lost.
  - Changed the type of `grade_level` on the `Student` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Made the column `language` on table `User` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "Subscription" DROP CONSTRAINT "Subscription_student_id_fkey";

-- DropForeignKey
ALTER TABLE "Teacher" DROP CONSTRAINT "Teacher_organization_id_fkey";

-- DropForeignKey
ALTER TABLE "_ParentStudent" DROP CONSTRAINT "_ParentStudent_A_fkey";

-- DropForeignKey
ALTER TABLE "_ParentStudent" DROP CONSTRAINT "_ParentStudent_B_fkey";

-- DropIndex
DROP INDEX "Student_subscription_id_key";

-- DropIndex
DROP INDEX "User_phone_key";

-- AlterTable
ALTER TABLE "ApprovalRequest" ALTER COLUMN "status" SET DEFAULT 'pending';

-- AlterTable
ALTER TABLE "DataRequest" ALTER COLUMN "status" SET DEFAULT 'pending';

-- AlterTable
ALTER TABLE "Flag" ALTER COLUMN "status" SET DEFAULT 'open';

-- AlterTable
ALTER TABLE "Lesson" ADD COLUMN     "subject" TEXT,
ALTER COLUMN "status" SET DEFAULT 'scheduled';

-- AlterTable
ALTER TABLE "Notification" ADD COLUMN     "parent_id" INTEGER,
ADD COLUMN     "teacher_id" INTEGER,
ALTER COLUMN "status" SET DEFAULT 'sent';

-- AlterTable
ALTER TABLE "Payment" ALTER COLUMN "status" SET DEFAULT 'pending';

-- AlterTable
ALTER TABLE "Report" ALTER COLUMN "status" SET DEFAULT 'generated';

-- AlterTable
ALTER TABLE "Student" DROP COLUMN "subscription_id",
DROP COLUMN "grade_level",
ADD COLUMN     "grade_level" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "Subscription" ALTER COLUMN "status" SET DEFAULT 'pending';

-- AlterTable
ALTER TABLE "Teacher" ADD COLUMN     "rating" DOUBLE PRECISION DEFAULT 0.0,
ALTER COLUMN "verified_status" SET DEFAULT 'pending';

-- AlterTable
ALTER TABLE "User" ALTER COLUMN "language" SET NOT NULL,
ALTER COLUMN "status" SET DEFAULT 'pending';

-- DropTable
DROP TABLE "_ParentStudent";

-- CreateTable
CREATE TABLE "TeacherStats" (
    "id" SERIAL NOT NULL,
    "teacher_id" INTEGER NOT NULL,
    "monthly_earnings" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "active_students" INTEGER NOT NULL DEFAULT 0,
    "teaching_hours" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "total_earnings" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TeacherStats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_ParentToStudent" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_ParentToStudent_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "TeacherStats_teacher_id_key" ON "TeacherStats"("teacher_id");

-- CreateIndex
CREATE INDEX "_ParentToStudent_B_index" ON "_ParentToStudent"("B");

-- AddForeignKey
ALTER TABLE "Teacher" ADD CONSTRAINT "Teacher_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Subscription" ADD CONSTRAINT "Subscription_student_id_fkey" FOREIGN KEY ("student_id") REFERENCES "Student"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_teacher_id_fkey" FOREIGN KEY ("teacher_id") REFERENCES "Teacher"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "Parent"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TeacherStats" ADD CONSTRAINT "TeacherStats_teacher_id_fkey" FOREIGN KEY ("teacher_id") REFERENCES "Teacher"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ParentToStudent" ADD CONSTRAINT "_ParentToStudent_A_fkey" FOREIGN KEY ("A") REFERENCES "Parent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ParentToStudent" ADD CONSTRAINT "_ParentToStudent_B_fkey" FOREIGN KEY ("B") REFERENCES "Student"("id") ON DELETE CASCADE ON UPDATE CASCADE;
