const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Check if test user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (existingUser) {
      console.log('Test user already exists');
      return;
    }
    
    // Create a test user with known password
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    const user = await prisma.user.create({
      data: {
        name: 'Test User',
        email: '<EMAIL>',
        phone: '+1234567890',
        password_hash: hashedPassword,
        role: 'student',
        language: 'en',
        status: 'active',
        student: {
          create: {
            grade_level: '10'
          }
        }
      },
      include: {
        student: true
      }
    });
    
    console.log('Test user created successfully:');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('User data:', JSON.stringify(user, null, 2));
    
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
