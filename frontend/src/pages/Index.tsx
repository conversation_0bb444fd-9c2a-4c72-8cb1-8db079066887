import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, BookOpen, Users, Video, MessageCircle, Award, Clock, Globe } from "lucide-react";
import HeroSection from "@/components/HeroSection";
import SubjectsSection from "@/components/SubjectsSection";
import HowItWorksSection from "@/components/HowItWorksSection";
import TeachersSection from "@/components/TeachersSection";
import PricingSection from "@/components/PricingSection";
import LanguageToggle from "@/components/LanguageToggle";

const Index = () => {
  const [language, setLanguage] = useState<"en" | "ar">("en");

  const content = {
    en: {
      nav: {
        home: "Home",
        subjects: "Subjects",
        teachers: "Teachers",
        pricing: "Pricing",
        contact: "Contact",
        login: "Login",
        signup: "Sign Up",
      },
    },
    ar: {
      nav: {
        home: "الرئيسية",
        subjects: "المواد",
        teachers: "المعلمون",
        pricing: "الأسعار",
        contact: "تواصل معنا",
        login: "تسجيل الدخول",
        signup: "إنشاء حساب",
      },
    },
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 ${language === "ar" ? "rtl" : "ltr"}`}>
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">EduConnect</span>
            </div>

            <div className="hidden md:flex items-center space-x-8">
              <a href="#home" className="text-gray-700 hover:text-blue-600 transition-colors">
                {content[language].nav.home}
              </a>
              <a href="#subjects" className="text-gray-700 hover:text-blue-600 transition-colors">
                {content[language].nav.subjects}
              </a>
              <a href="#teachers" className="text-gray-700 hover:text-blue-600 transition-colors">
                {content[language].nav.teachers}
              </a>
              <a href="#pricing" className="text-gray-700 hover:text-blue-600 transition-colors">
                {content[language].nav.pricing}
              </a>
            </div>

            <div className="flex items-center space-x-4">
              <LanguageToggle language={language} onLanguageChange={setLanguage} />
              <Link to="/login">
                <Button variant="outline" size="sm">
                  {content[language].nav.login}
                </Button>
              </Link>
              <Link to="/signup">
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                  {content[language].nav.signup}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main>
        <HeroSection language={language} />
        <SubjectsSection language={language} />
        <HowItWorksSection language={language} />
        <TeachersSection language={language} />
        <PricingSection language={language} />
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <BookOpen className="h-8 w-8 text-blue-400" />
                <span className="text-xl font-bold">EduConnect</span>
              </div>
              <p className="text-gray-400">{language === "en" ? "Connecting students with qualified tutors across the MENA region." : "ربط الطلاب بالمعلمين المؤهلين في منطقة الشرق الأوسط وشمال أفريقيا."}</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">{language === "en" ? "Quick Links" : "روابط سريعة"}</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {language === "en" ? "About Us" : "من نحن"}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {language === "en" ? "How it Works" : "كيف يعمل"}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {language === "en" ? "Privacy Policy" : "سياسة الخصوصية"}
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">{language === "en" ? "Support" : "الدعم"}</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {language === "en" ? "Help Center" : "مركز المساعدة"}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {language === "en" ? "Contact Us" : "تواصل معنا"}
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    {language === "en" ? "FAQ" : "الأسئلة الشائعة"}
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4">{language === "en" ? "Contact" : "تواصل معنا"}</h3>
              <p className="text-gray-400 mb-2"><EMAIL></p>
              <p className="text-gray-400">+964 XXX XXX XXXX</p>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 EduConnect. {language === "en" ? "All rights reserved." : "جميع الحقوق محفوظة."}</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
