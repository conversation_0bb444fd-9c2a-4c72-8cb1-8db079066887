import { Button } from "@/components/ui/button";
import { Globe } from "lucide-react";

interface LanguageToggleProps {
  language: "en" | "ar";
  onLanguageChange: (lang: "en" | "ar") => void;
}

const LanguageToggle = ({ language, onLanguageChange }: LanguageToggleProps) => {
  return (
    <div className="flex items-center space-x-1">
      <Globe className="h-4 w-4 text-gray-500" />
      <Button variant={language === "en" ? "default" : "ghost"} size="sm" onClick={() => onLanguageChange("en")} className="text-xs px-2 py-1">
        EN
      </Button>
      <Button variant={language === "ar" ? "default" : "ghost"} size="sm" onClick={() => onLanguageChange("ar")} className="text-xs px-2 py-1">
        العربية
      </Button>
    </div>
  );
};

export default LanguageToggle;
